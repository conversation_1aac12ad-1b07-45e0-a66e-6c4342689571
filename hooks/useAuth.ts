import { useState, useEffect } from 'react'
import { auth } from '@/app/lib/firebase'
import { User } from 'firebase/auth'

export function useAuth() {
    const [user, setUser] = useState<User | null>(null)
    const [loading, setLoading] = useState(true)

    useEffect(() => {
        const unsubscribe = auth.onAuthStateChanged((user) => {
            setUser(user)
            setLoading(false)
        })

        return unsubscribe
    }, [])

    return { user, loading }
}