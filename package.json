{"name": "yeni", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "postbuild": "next-sitemap", "start": "next start", "lint": "next lint"}, "dependencies": {"firebase": "^11.0.2", "framer-motion": "^11.11.17", "lucide-react": "^0.460.0", "next": "15.0.3", "next-sitemap": "^4.2.3", "next-themes": "^0.4.3", "react": "18.2.0", "react-dom": "18.2.0", "react-intersection-observer": "^9.13.1", "react-schemaorg": "^2.0.0", "schema-dts": "^1.1.2"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^8", "eslint-config-next": "15.0.3", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}, "resolutions": {"react": "18.2.0", "react-dom": "18.2.0"}}