import { initializeApp } from "firebase/app";
import { getAuth } from "@firebase/auth";
import { getFirestore } from "@firebase/firestore";
import { getStorage } from "@firebase/storage";

export const firebaseConfig = {
    apiKey: "AIzaSyC_vz04inEwoCyvwBxBtYNwxOUn2VLF4Oc",
    authDomain: "felixart-b4aad.firebaseapp.com",
    projectId: "felixart-b4aad",
    storageBucket: "felixart-b4aad.firebasestorage.app",
    messagingSenderId: "763164948948",
    appId: "1:763164948948:web:b81c557a112dbb828afe41",
    measurementId: "G-ET8624DY2K"
};

const app = initializeApp(firebaseConfig);
export const auth = getAuth(app);
export const db = getFirestore(app);
export const storage = getStorage(app);

if (typeof window !== 'undefined') {
    import("firebase/analytics").then(({ getAnalytics }) => {
        getAnalytics(app);
    }).catch((error) => {
        console.error("Firebase Analytics yüklenemedi:", error);
    });
}