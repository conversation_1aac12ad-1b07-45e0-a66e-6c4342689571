// components/About.tsx
'use client'
import {motion} from 'framer-motion'

export default function Join() {
    return (
        <section className="relative py-20 overflow-hidden">
            {/* Reverse Wave Animation */}
            <motion.div
                className="absolute inset-0 pointer-events-none"
                initial={{opacity: 0}}
                animate={{opacity: 1}}
                transition={{duration: 1}}
            >
                <svg
                    viewBox="0 0 1200 800"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                    className="w-full h-full"
                    preserveAspectRatio="none"
                >
                    <motion.path
                        d="M1200 400C900 400 900 600 600 600C300 600 300 200 0 200M1200 400C900 400 900 600 600 600C300 600 300 200 0 200"
                        stroke="rgba(0,0,0,0.1)"
                        strokeWidth="1.5"
                        initial={{pathLength: 0}}
                        animate={{pathLength: 1}}
                        transition={{duration: 4, ease: "linear", loop: Infinity}}
                    />
                </svg>
            </motion.div>

            <div className="container mx-auto px-4 sm:px-6 lg:px-8">
                <div className="max-w-4xl">
                    <motion.h2
                        initial={{opacity: 0, y: 20}}
                        whileInView={{opacity: 1, y: 0}}
                        viewport={{once: true}}
                        className="text-4xl font-bold mb-4"
                    >
                        Join the best talents
                    </motion.h2>
                    <motion.p
                        initial={{opacity: 0, y: 20}}
                        whileInView={{opacity: 1, y: 0}}
                        viewport={{once: true}}
                        transition={{delay: 0.1}}
                        className="text-gray-600 mb-8"
                    >
                        If you are a skilled developer or designer, reach us to work on our projects remote.
                    </motion.p>

                    <motion.div
                        initial={{opacity: 0, y: 20}}
                        whileInView={{opacity: 1, y: 0}}
                        viewport={{once: true}}
                        transition={{delay: 0.2}}
                    >

                        <p className="text-gray-600 mb-8">
                            We work on new-age projects with the best professionals for selected clients and projects.
                        </p>
                        <button
                            className="bg-[#111] text-white px-6 py-3 rounded text-md hover:bg-black transition-colors"
                            onClick={() => window.location.href = 'mailto:<EMAIL>'}>
                            Send CV
                        </button>
                    </motion.div>

                    <div className="grid grid-cols-2 md:grid-cols-4 gap-8 mt-20">
                        {stats.map((stat, index) => (
                            <motion.div
                                key={stat.label}
                                initial={{opacity: 0, y: 20}}
                                whileInView={{opacity: 1, y: 0}}
                                viewport={{once: true}}
                                transition={{delay: 0.1 * (index + 1)}}
                                className="bg-gray-50 p-6 rounded-lg"
                            >
                                <div className="mb-2">
                                    {stat.icon}
                                </div>
                                <div className="text-4xl font-bold mb-2">{stat.value}</div>
                                <div className="text-gray-600">{stat.label}</div>
                            </motion.div>
                        ))}
                    </div>
                </div>
            </div>
        </section>
    )
}

const stats = [
    {
        icon: <span className="text-2xl">😊</span>,
        value: "10+",
        label: "Years of operation"
    },
    {
        icon: <span className="text-2xl">📱</span>,
        value: "100+",
        label: "Projects"
    },
    {
        icon: <span className="text-2xl">👥</span>,
        value: "20+",
        label: "Experts"
    },
    {
        icon: <span className="text-2xl">🤝</span>,
        value: "100+",
        label: "Clients"
    }
]