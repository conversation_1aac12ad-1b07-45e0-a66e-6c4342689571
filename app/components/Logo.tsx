'use client'
import { useTheme } from 'next-themes'
import { useState, useEffect } from 'react'

export default function Logo() {
    const { theme } = useTheme()
    const [mounted, setMounted] = useState(false)

    useEffect(() => {
        setMounted(true)
    }, [])

    if (!mounted) return <span className="w-8 h-8 block" />

    return (
        <>
            {theme === 'dark' ? (
                <svg width="32" height="32" viewBox="0 0 1000 1000" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <defs>
                        <style>
                            {`.st0 { fill: #fff; }`}
                        </style>
                    </defs>
                    <path className="st0" d="M178.3,985c0-355.2,288.4-643.2,643.4-643.2h0v162.4c-265.2.7-480,215.6-480.5,480.7h-162.9Z"/>
                    <path className="st0" d="M178.3,260.8v242.6c147-196.1,379.3-324.9,643.4-325.2V15c-247.3,0-472.3,93.5-643.4,245.8Z"/>
                </svg>
            ) : (
                <svg width="32" height="32" viewBox="0 0 1000 1000" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <defs>
                        <style>
                            {`.st0 { fill: #161e27; }`}
                        </style>
                    </defs>
                    <path className="st0" d="M178.3,985c0-355.2,288.4-643.2,643.4-643.2h0v162.4c-265.2.7-480,215.6-480.5,480.7h-162.9Z"/>
                    <path className="st0" d="M178.3,260.8v242.6c147-196.1,379.3-324.9,643.4-325.2V15c-247.3,0-472.3,93.5-643.4,245.8Z"/>
                </svg>
            )}
        </>
    )
}