// components/Offices/Offices.tsx
'use client'
import { motion } from 'framer-motion'
import Image from 'next/image'

const offices = [
    {
        city: 'Cappadocia',
        email: '<EMAIL>',
        image: '/images/kapadokya.jpg',
        description: 'Historical region in central Turkey'
    },
    {
        city: 'London',
        email: '<EMAIL>',
        image: '/images/londra.jpg',
        description: 'Capital of England and the United Kingdom'
    }
]

export default function Offices() {
    return (
        <section className="py-20 ">
            <div className="container mx-auto px-4 sm:px-6 lg:px-8">
                <motion.p
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    viewport={{ once: true }}
                    className="text-gray-600 mb-4"
                >
                    We operate remotely but there are still some cities you can meet with us.
                </motion.p>

                <motion.h2
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    viewport={{ once: true }}
                    className="text-4xl font-bold mb-6"
                >
                    Our offices
                </motion.h2>

                <motion.p
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    viewport={{ once: true }}
                    className="text-gray-600 mb-12"
                >
                    Please contact us for meeting.
                </motion.p>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                    {offices.map((office, index) => (
                        <motion.div
                            key={office.city}
                            initial={{ opacity: 0, y: 20 }}
                            whileInView={{ opacity: 1, y: 0 }}
                            viewport={{ once: true }}
                            transition={{ delay: index * 0.1 }}
                            className="group"
                        >
                            <div className="relative aspect-[4/3] mb-4 rounded-lg overflow-hidden">
                                <Image
                                    src={office.image}
                                    alt={office.city}
                                    fill
                                    className="object-cover transition-transform duration-300 group-hover:scale-105"
                                />
                                <div className="absolute inset-0 bg-black/20 transition-opacity duration-300 group-hover:opacity-0" />
                            </div>
                            <a
                                href={`mailto:${office.email}`}
                                className="text-gray-600 hover:text-black transition-colors text-sm"
                            >
                                {office.email}
                            </a>
                        </motion.div>
                    ))}
                </div>
            </div>
        </section>
    )
}