'use client'
import { motion } from 'framer-motion'

export default function WaveAnimation() {
    return (
        <motion.div
            className="absolute top-0 right-0 w-1/2 h-[300px] pointer-events-none"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 1 }}
        >
            <svg
                viewBox="0 0 500 200"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
                className="w-full h-full"
            >
                <motion.path
                    d="M0 100C125 100 125 150 250 150C375 150 375 50 500 50"
                    stroke="rgba(0,0,0,0.1)"
                    strokeWidth="2"
                    initial={{ pathLength: 0 }}
                    animate={{ pathLength: 1 }}
                    transition={{ duration: 2, ease: "easeInOut" }}
                />
            </svg>
        </motion.div>
    )
}