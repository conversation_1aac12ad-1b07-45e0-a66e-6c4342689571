// components/Header.tsx
'use client'
import { useAuth } from '@/hooks/useAuth'
import { LogOut, Moon, Sun } from 'lucide-react'
import { auth } from '@/app/lib/firebase'
import { signOut } from 'firebase/auth'
import Link from 'next/link'
import { useTheme } from 'next-themes'
import { useState, useEffect } from 'react'
import Logo from './Logo'

const ADMIN_EMAIL = '<EMAIL>'

export default function Header() {
    const { user } = useAuth()
    const { theme, setTheme } = useTheme()
    const [mounted, setMounted] = useState(false)
    const isAdmin = user?.email === ADMIN_EMAIL

    useEffect(() => {
        setMounted(true)
    }, [])

    const handleLogout = async () => {
        try {
            await signOut(auth)
        } catch (error) {
            console.error('Error signing out:', error)
        }
    }

    return (
        <header className="fixed w-full z-50 bg-white dark:bg-gray-900 transition-colors">
            <div className="container mx-auto px-8 py-6">
                <div className="flex justify-between items-center">
                    <Link
                        href="/"
                        className="hover:opacity-80 transition-opacity"
                        title="feliXart Home"
                    >
                        <Logo />
                    </Link>
                    <div className="flex items-center gap-6">
                        {/* Theme Toggle */}
{mounted && (
    <button
        onClick={() => setTheme(theme === 'dark' ? 'light' : 'dark')}
        className="p-2 rounded hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors block md:hidden"
        title="Toggle theme"
    >
        {theme === 'dark' ? (
            <Sun size={18} className="text-gray-200 hover:text-gray-100" />
        ) : (
            <Moon size={18} className="text-gray-600 hover:text-gray-800" />
        )}
    </button>
)}

                        {/* Admin Controls */}
                        {isAdmin && (
                            <button
                                onClick={handleLogout}
                                className="text-md text-gray-500 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-200 flex items-center gap-2 transition-colors"
                                title="Logout"
                            >
                                <LogOut size={16} />
                                <span className="hidden sm:inline">Logout</span>
                            </button>
                        )}

                        {/* Navigation Links */}
                        <nav className="flex items-center gap-6">
                            {/*
                            <Link
                                href="/blog"
                                className="text-md text-gray-600 hover:text-gray-900 dark:text-gray-300 dark:hover:text-white transition-colors"
                            >
                                Blog
                            </Link>
                            <Link
                                href="/contact"
                                className="text-md text-gray-600 hover:text-gray-900 dark:text-gray-300 dark:hover:text-white transition-colors"
                            >
                                Contact
                            </Link>
                            */}
                        </nav>
                    </div>
                </div>
            </div>
            <div className="w-full h-px bg-gray-100 dark:bg-gray-800 transition-colors" />
        </header>
    )
}