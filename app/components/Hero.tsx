'use client'
import { motion } from 'framer-motion'
import { Linkedin } from 'lucide-react'

export default function Hero() {
    return (
        <section className="relative pt-32 pb-16">
            {/* Wave Animation */}
            <motion.div
                className="absolute inset-0 bottom-16 pointer-events-none"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 1 }}
            >
                <svg
                    viewBox="0 0 1200 800"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                    className="w-full h-full"
                    preserveAspectRatio="none"
                >
                    <motion.path
                        d="M0 400C300 400 300 600 600 600C900 600 900 200 1200 200M0 400C300 400 300 600 600 600C900 600 900 200 1200 200"
                        stroke="rgba(0,0,0,0.1)"
                        strokeWidth="1.5"
                        initial={{ pathLength: 0 }}
                        animate={{ pathLength: 1 }}
                        transition={{ duration: 4, ease: "linear", loop: Infinity }}
                    />
                </svg>
            </motion.div>
            {/* Content */}
            <div className="container mx-auto px-8">
                <motion.div
                    className="max-w-xl"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.8 }}
                >
                    <h1 className="text-5xl font-bold mb-4">
                        New Age Agency.
                    </h1>
                    <br/>
                    <p className="text-xl mb-2">
                        Mobile Apps, Web Apps, AR, E-Commerce.
                    </p>
                    <br/>
                    <p className="text-gray-500 text-base mb-6">#FEELTHEFUTURE</p>

                </motion.div>
                {/* Bottom Line with Social Links */}
                <div className="mt-20 flex items-center">
                    <div className="w-[93%] border-t border-gray-800"></div>
                    <div className="flex gap-4 ml-auto text-md">
                        <a href="https://linkedin.com/company/felixart" className="text-gray-900 hover:text-red-500 flex items-center gap-2">
                            <Linkedin size={16} />
                            Linkedin
                        </a>
                    </div>
                </div>
            </div>
        </section>
    )
}