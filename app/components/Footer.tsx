import {Twitter, Linkedin, Youtube} from 'lucide-react'
import Link from 'next/link'

export default function Footer() {
    return (
        <footer className="py-12">
            <div className="container mx-auto px-8">
                <div className="border-t border-gray-100 pt-12 flex flex-col items-center">
                    <div className="flex justify-between items-start w-full">
                        <div>
                            <h2 className="text-xl font-bold mb-4">Get in touch!</h2>
                            <div className="space-y-2">
                                <p className="text-md">
                                    Email: <Link href="mailto:<EMAIL>"
                                                 className="text-gray-500"><EMAIL></Link>
                                </p>
                                <p className="text-md">
                                    Phone: <Link href="tel:+905334207792" className="text-gray-500">+90 533 420 77
                                    92</Link>
                                </p>
                                <p className="text-md">
                                    WhatsApp: <Link href="https://wa.me/+447383217442" className="text-gray-500">+44 7383
                                    217 442</Link></p>
                            </div>
                        </div>
                        <div className="flex gap-4">
                            <Link href="https://twitter.com/felixartcomtr"
                                  className="text-gray-400 hover:text-black transition-colors">
                                <Twitter size={20}/>
                            </Link>
                            <Link href="https://linkedin.com/company/felixart"
                                  className="text-gray-400 hover:text-black transition-colors">
                                <Linkedin size={20}/>
                            </Link>
                            <Link href="https://youtube.com/@felixartstudios"
                                  className="text-gray-400 hover:text-black transition-colors">
                                <Youtube size={20}/>
                            </Link>
                        </div>
                    </div>
                    <div className="flex items-center justify-between w-full mt-8">
                        <p className="text-lg font-semibold">Check Out Our Most Interesting Works</p>
                        <Link href="https://www.youtube.com/@feliXartstudios" passHref>
                            <button
                                className="bg-[#111] text-white px-6 py-3 rounded text-md hover:bg-black transition-colors">
                                YouTube
                            </button>
                        </Link>
                    </div>
                    <div className="border-t border-gray-100 w-full mt-8 pt-4 text-md text-gray-400">
                        © 2024 Felixart
                    </div>
                </div>
            </div>
        </footer>
    )
}