// components/Portfolio/ProjectModal.tsx
'use client'
import { useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Project } from '@/app/types/project'
import Image from 'next/image'
import Link from 'next/link'
import { X, Globe, Tag, Layers } from 'lucide-react'

interface ProjectModalProps {
    project: Project;
    isOpen: boolean;
    onCloseAction: () => void;         // onClose -> onCloseAction
}

export function ProjectModal({ project, isOpen, onCloseAction }: ProjectModalProps) {
    useEffect(() => {
        const handleEsc = (e: KeyboardEvent) => {
            if (e.key === 'Escape') onCloseAction()
        }
        window.addEventListener('keydown', handleEsc)
        return () => window.removeEventListener('keydown', handleEsc)
    }, [onCloseAction])

    return (
        <AnimatePresence>
            {isOpen && (
                <>
                    <motion.div
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        exit={{ opacity: 0 }}
                        onClick={onCloseAction}
                        className="fixed inset-0 bg-black/60 z-50"
                    />
                    <motion.div
                        initial={{ opacity: 0, scale: 0.95, y: 20 }}
                        animate={{ opacity: 1, scale: 1, y: 0 }}
                        exit={{ opacity: 0, scale: 0.95, y: 20 }}
                        className="fixed inset-4 md:inset-10 bg-white rounded z-50 overflow-auto"
                    >
                        <div className="sticky top-0 bg-white z-10 p-4 border-b flex justify-between items-center">
                            <h2 className="text-xl font-bold">{project.title}</h2>
                            <button
                                onClick={onCloseAction}
                                className="p-2 hover:bg-gray-100 rounded transition-colors"
                                aria-label="Close modal"
                            >
                                <X size={20} />
                            </button>
                        </div>

                        <div className="p-4 md:p-6">
                            <div className="relative aspect-video mb-6 rounded overflow-hidden group">
                                <Image
                                    src={project.image}
                                    alt={`${project.title} project image`}
                                    fill
                                    className="object-cover transition-transform duration-300 group-hover:scale-105"
                                    sizes="(max-width: 768px) 100vw, (max-width: 1200px) 80vw"
                                    priority
                                />
                            </div>

                            <div className="grid md:grid-cols-[2fr,1fr] gap-6">
                                <div className="space-y-6">
                                    <div>
                                        <h3 className="text-lg font-semibold mb-2">About the Project</h3>
                                        <p className="text-gray-600 leading-relaxed whitespace-pre-wrap">
                                            {project.description}
                                        </p>
                                    </div>

                                    {project.technologies && project.technologies.length > 0 && (
                                        <div>
                                            <h3 className="text-lg font-semibold mb-3 flex items-center gap-2">
                                                <Layers size={18} />
                                                Used Technologies
                                            </h3>
                                            <div className="flex flex-wrap gap-2">
                                                {project.technologies.map((tech) => (
                                                    <span
                                                        key={tech}
                                                        className="px-3 py-1.5 bg-gray-100 rounded text-md font-medium"
                                                    >
                                                        {tech}
                                                    </span>
                                                ))}
                                            </div>
                                        </div>
                                    )}
                                </div>

                                <div className="space-y-6">
                                    <div className="p-4 bg-gray-50 rounded space-y-4">
                                        <div className="flex items-start gap-3">
                                            <Tag size={18} className="mt-1 text-gray-500" />
                                            <div>
                                                <h4 className="font-medium">Category</h4>
                                                <p className="text-md text-gray-600">{project.category}</p>
                                            </div>
                                        </div>

                                        {project.link && (
                                            <div className="flex items-start gap-3">
                                                <Globe size={18} className="mt-1 text-gray-500" />
                                                <div>
                                                    <h4 className="font-medium">Project URL</h4>
                                                    <a
                                                        href={project.link}
                                                        target="_blank"
                                                        rel="noopener noreferrer"
                                                        className="text-md text-blue-600 hover:underline"
                                                    >
                                                        Visit Website
                                                    </a>
                                                </div>
                                            </div>
                                        )}
                                    </div>

                                    <div className="flex gap-3">
                                        {project.link && (
                                            <a
                                                href={project.link}
                                                target="_blank"
                                                rel="noopener noreferrer"
                                                className="flex-1 bg-black text-white px-4 py-2 rounded text-md text-center hover:bg-gray-900 transition-colors"
                                            >
                                                Visit Project
                                            </a>
                                        )}
                                        <Link
                                            href={`/projects/${project.slug}`}
                                            className="flex-1 border border-gray-200 px-4 py-2 rounded text-md text-center hover:bg-gray-50 transition-colors"
                                        >
                                            View Details
                                        </Link>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </motion.div>
                </>
            )}
        </AnimatePresence>
    )
}