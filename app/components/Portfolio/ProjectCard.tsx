// components/Portfolio/ProjectCard.tsx
'use client'
import { motion } from 'framer-motion'
import Image from 'next/image'
import { Project } from '@/app/types/project'
import { useState } from 'react'
import { Pencil, Trash2 } from 'lucide-react'

interface ProjectCardProps {
    project: Project;
    onProjectClickAction: () => void;  // onProjectClick -> onProjectClickAction
    onEditAction?: () => void;         // onEdit -> onEditAction
    onDeleteAction?: () => void;       // onDelete -> onDeleteAction
}

export default function ProjectCard({
                                        project,
                                        onProjectClickAction,
                                        onEditAction,
                                        onDeleteAction
                                    }: ProjectCardProps) {
    const [imageLoading, setImageLoading] = useState(true)

    const handleEdit = (e: React.MouseEvent) => {
        e.stopPropagation()
        onEditAction?.()
    }

    const handleDelete = (e: React.MouseEvent) => {
        e.stopPropagation()
        onDeleteAction?.()
    }

    return (
        <motion.div
            whileHover={{ y: -5 }}
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.3 }}
            className="group cursor-pointer relative"
            onClick={onProjectClickAction}
        >
            {(onEditAction || onDeleteAction) && (
                <div className="absolute top-4 right-4 z-20 flex gap-2">
                    {onEditAction && (
                        <button
                            onClick={handleEdit}
                            className="p-2 bg-white rounded shadow-lg hover:bg-gray-100 transition-colors"
                            title="Edit project"
                            aria-label="Edit project"
                        >
                            <Pencil size={16} />
                        </button>
                    )}
                    {onDeleteAction && (
                        <button
                            onClick={handleDelete}
                            className="p-2 bg-white rounded shadow-lg hover:bg-gray-100 transition-colors"
                            title="Delete project"
                            aria-label="Delete project"
                        >
                            <Trash2 size={16} />
                        </button>
                    )}
                </div>
            )}

            <div className="relative aspect-[4/3] bg-gray-50 rounded overflow-hidden transition-all duration-300 group-hover:shadow-xl">
                {imageLoading && (
                    <div className="absolute inset-0 bg-gray-100 animate-pulse" />
                )}

                <Image
                    src={project.image}
                    alt={`${project.title} project in ${project.category} category`}
                    fill
                    className={`
                        object-cover transition-all duration-300 
                        ${imageLoading ? 'opacity-0 scale-110' : 'opacity-100 scale-100'}
                        group-hover:scale-105
                    `}
                    sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw"
                    onLoadingComplete={() => setImageLoading(false)}
                    priority={false}
                />

                <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-all duration-300 flex items-center justify-center">
                    <span className="bg-white text-black px-6 py-3 rounded text-md transform translate-y-4 opacity-0 group-hover:translate-y-0 group-hover:opacity-100 transition-all duration-300">
                        View Project
                    </span>
                </div>
            </div>

            <div className="pt-4">
                <h3 className="text-lg font-medium group-hover:text-gray-600 transition-colors">
                    {project.title}
                </h3>
                <p className="text-md text-gray-500">{project.category}</p>
            </div>
        </motion.div>
    )
}