// components/Portfolio/ProjectForm.tsx
'use client'
import {useState, useRef} from 'react'
import {Project} from '@/app/types/project'
import {X, Upload} from 'lucide-react'
import {useFirebase} from '@/app/context/FirebaseContext'
import Image from 'next/image'

interface ProjectFormProps {
    project?: Omit<Project, 'id' | 'createdAt' | 'updatedAt'>;
    onSubmitAction: (project: Omit<Project, 'id' | 'createdAt' | 'updatedAt'>) => Promise<void>;  // onSubmit -> onSubmitAction
    onCloseAction: () => void;         // onClose -> onCloseAction
}
export function ProjectForm({project, onSubmitAction, onCloseAction}: ProjectFormProps) {
    const [formData, setFormData] = useState<Omit<Project, 'id' | 'createdAt' | 'updatedAt'>>({
        title: project?.title || '',
        slug: project?.slug || '',
        description: project?.description || '',
        category: project?.category || 'Mobile/Web',
        image: project?.image || '',
        technologies: project?.technologies || [],
        link: project?.link || ''
    })
    const [technology, setTechnology] = useState('')
    const [loading, setLoading] = useState(false)
    const [uploading, setUploading] = useState(false)
    const [errors, setErrors] = useState<Record<string, string>>({})
    const fileInputRef = useRef<HTMLInputElement>(null)
    const {uploadFile} = useFirebase()

    const validate = (): boolean => {
        const newErrors: Record<string, string> = {}

        if (!formData.title.trim()) {
            newErrors.title = 'Project title is required'
        }

        if (!formData.description.trim()) {
            newErrors.description = 'Project description is required'
        }

        if (!formData.image) {
            newErrors.image = 'Project image is required'
        }

        setErrors(newErrors)
        return Object.keys(newErrors).length === 0
    }

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault()
        if (uploading || !validate()) return

        setLoading(true)
        try {
            await onSubmitAction(formData)
            onCloseAction()
        } catch (error) {
            console.error('Error submitting project:', error)
            setErrors({submit: 'An error occurred while saving the project'})
        } finally {
            setLoading(false)
        }
    }

    const handleImageUpload = async (file: File) => {
        if (!file.type.startsWith('image/')) {
            setErrors({image: 'Please select a valid image file'})
            return
        }

        setUploading(true)
        try {
            const path = `projects/${Date.now()}_${file.name}`
            const imageUrl = await uploadFile(file, path)
            setFormData(prev => ({...prev, image: imageUrl}))
            setErrors(prev => ({...prev, image: ''}))
        } catch (error) {
            console.error('Error uploading image:', error)
            setErrors(prev => ({...prev, image: 'An error occurred while uploading the image'}))
        } finally {
            setUploading(false)
        }
    }

    const handleTechnologyAdd = (e: React.KeyboardEvent) => {
        if (e.key === 'Enter' && technology.trim()) {
            e.preventDefault()
            if (formData.technologies && !formData.technologies.includes(technology.trim())) {
                setFormData(prev => ({
                    ...prev,
                    technologies: [...(prev.technologies || []), technology.trim()]
                }))
            }
            setTechnology('')
        }
    }

    const handleTechnologyRemove = (techToRemove: string) => {
        setFormData(prev => ({
            ...prev,
            technologies: prev.technologies ? prev.technologies.filter(tech => tech !== techToRemove) : []
        }))
    }

    const generateSlug = (title: string): string => {
        return title
            .toLowerCase()
            .replace(/ğ/g, 'g')
            .replace(/ü/g, 'u')
            .replace(/ş/g, 's')
            .replace(/ı/g, 'i')
            .replace(/ö/g, 'o')
            .replace(/ç/g, 'c')
            .replace(/[^a-z0-9]+/g, '-')
            .replace(/(^-|-$)/g, '')
    }

    return (
        <div className="fixed inset-0 bg-black/60 z-50 flex items-center justify-center p-4">
            <div className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
                <div className="p-6">
                    <div className="flex justify-between items-center mb-6">
                        <h2 className="text-2xl font-bold">
                            {project ? 'Edit Project' : 'Add New Project'}
                        </h2>
                        <button
                            onClick={onCloseAction}
                            className="p-2 hover:bg-gray-100 rounded transition-colors"
                            aria-label="Close form"
                        >
                            <X size={24}/>
                        </button>
                    </div>

                    <form onSubmit={handleSubmit} className="space-y-6">
                        <div>
                            <label className="block text-md font-medium mb-2" htmlFor="title">
                                Project Title
                            </label>
                            <input
                                id="title"
                                type="text"
                                value={formData.title}
                                onChange={(e) => {
                                    const title = e.target.value
                                    setFormData(prev => ({
                                        ...prev,
                                        title,
                                        slug: generateSlug(title)
                                    }))
                                    setErrors(prev => ({...prev, title: ''}))
                                }}
                                className={`w-full px-4 py-2 border rounded focus:outline-none focus:ring-2 focus:ring-black/5 ${
                                    errors.title ? 'border-red-500' : 'border-gray-200'
                                }`}
                            />
                            {errors.title && (
                                <p className="mt-1 text-sm text-red-500">{errors.title}</p>
                            )}
                        </div>

                        <div>
                            <label className="block text-md font-medium mb-2" htmlFor="slug">
                                Slug
                            </label>
                            <input
                                id="slug"
                                type="text"
                                value={formData.slug}
                                readOnly
                                className="w-full px-4 py-2 border rounded bg-gray-50"
                            />
                        </div>

                        <div>
                            <label className="block text-md font-medium mb-2" htmlFor="description">
                                Description
                            </label>
                            <textarea
                                id="description"
                                value={formData.description}
                                onChange={(e) => {
                                    setFormData(prev => ({
                                        ...prev,
                                        description: e.target.value
                                    }))
                                    setErrors(prev => ({...prev, description: ''}))
                                }}
                                rows={4}
                                className={`w-full px-4 py-2 border rounded focus:outline-none focus:ring-2 focus:ring-black/5 ${
                                    errors.description ? 'border-red-500' : 'border-gray-200'
                                }`}
                            />
                            {errors.description && (
                                <p className="mt-1 text-sm text-red-500">{errors.description}</p>
                            )}
                        </div>

                        <div>
                            <label className="block text-md font-medium mb-2" htmlFor="category">
                                Category
                            </label>
                            <select
                                id="category"
                                value={formData.category}
                                onChange={(e) => setFormData(prev => ({
                                    ...prev,
                                    category: e.target.value as Project['category']
                                }))}
                                className="w-full px-4 py-2 border border-gray-200 rounded focus:outline-none focus:ring-2 focus:ring-black/5"
                            >
                                <option value="Mobile/Web">Mobile/Web</option>
                                <option value="Metaverse">Metaverse</option>
                                <option value="Digital Marketing">Digital Marketing</option>
                            </select>
                        </div>

                        <div>
                            <label className="block text-md font-medium mb-2">
                                Project Image
                            </label>
                            <input
                                type="file"
                                accept="image/*"
                                ref={fileInputRef}
                                className="hidden"
                                onChange={(e) => {
                                    const file = e.target.files?.[0]
                                    if (file) handleImageUpload(file)
                                }}
                            />
                            <div className="flex gap-4 items-center">
                                <button
                                    type="button"
                                    onClick={() => fileInputRef.current?.click()}
                                    disabled={uploading}
                                    className="px-4 py-2 border rounded hover:bg-gray-50 flex items-center gap-2 disabled:opacity-50"
                                >
                                    <Upload size={16}/>
                                    {uploading ? 'Uploading...' : 'Upload Image'}
                                </button>
                                {formData.image && (
                                    <div className="relative w-20 h-20">
                                        <Image
                                            src={formData.image}
                                            alt="Project image preview"
                                            fill
                                            className="object-cover rounded"
                                        />
                                    </div>
                                )}
                            </div>
                            {errors.image && (
                                <p className="mt-1 text-sm text-red-500">{errors.image}</p>
                            )}
                        </div>

                        <div>
                            <label className="block text-md font-medium mb-2">
                                Technologies
                            </label>
                            <input
                                type="text"
                                value={technology}
                                onChange={(e) => setTechnology(e.target.value)}
                                onKeyDown={handleTechnologyAdd}
                                placeholder="Add with Enter key"
                                className="w-full px-4 py-2 border border-gray-200 rounded focus:outline-none focus:ring-2 focus:ring-black/5"
                            />
                            <div className="flex flex-wrap gap-2 mt-2">
                                {formData.technologies && formData.technologies.map((tech) => (
                                    <span
                                        key={tech}
                                        className="px-3 py-1 bg-gray-100 rounded text-md flex items-center gap-2"
                                    >
        {tech}
                                        <button
                                            type="button"
                                            onClick={() => handleTechnologyRemove(tech)}
                                            className="text-gray-500 hover:text-gray-700"
                                            aria-label={`Remove ${tech} technology`}
                                        >
            ×
        </button>
    </span>
                                ))}
                            </div>
                        </div>

                        <div>
                            <label className="block text-md font-medium mb-2" htmlFor="link">
                                Project URL
                            </label>
                            <input
                                id="link"
                                type="url"
                                value={formData.link}
                                onChange={(e) => setFormData(prev => ({
                                    ...prev,
                                    link: e.target.value
                                }))}
                                className="w-full px-4 py-2 border border-gray-200 rounded focus:outline-none focus:ring-2 focus:ring-black/5"
                                placeholder="https://"
                            />
                        </div>

                        {errors.submit && (
                            <p className="text-sm text-red-500">{errors.submit}</p>
                        )}

                        <div className="flex gap-4">
                            <button
                                type="button"
                                onClick={onCloseAction}
                                className="flex-1 px-4 py-2 border border-gray-200 rounded hover:bg-gray-50 transition-colors"
                            >
                                Cancel
                            </button>
                            <button
                                type="submit"
                                disabled={loading || uploading}
                                className="flex-1 px-4 py-2 bg-black text-white rounded hover:bg-gray-900 transition-colors disabled:opacity-50"
                            >
                                {loading ? 'Saving...' : (project ? 'Update' : 'Create')}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    )
}