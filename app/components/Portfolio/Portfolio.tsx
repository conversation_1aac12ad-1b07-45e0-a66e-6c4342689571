// components/Portfolio/Portfolio.tsx
'use client'
import {useState, useEffect} from 'react'
import {collection, query, getDocs, addDoc, updateDoc, deleteDoc, doc} from 'firebase/firestore'
import {db} from '@/app/lib/firebase'
import {Project} from '@/app/types/project'
import ProjectCard from './ProjectCard'
import {ProjectModal} from './ProjectModal'
import {ProjectForm} from './ProjectForm'
import {motion} from 'framer-motion'
import {useAuth} from '@/hooks/useAuth'

interface PortfolioProps {
    showTitle?: boolean;
    maxProjects?: number;
}

const ADMIN_EMAIL = '<EMAIL>'

export default function Portfolio({showTitle = true, maxProjects}: PortfolioProps) {
    const [activeCategory, setActiveCategory] = useState<Project['category'] | 'All'>('All')
    const [selectedProject, setSelectedProject] = useState<Project | null>(null)
    const [projects, setProjects] = useState<Project[]>([])
    const [isFormOpen, setIsFormOpen] = useState(false)
    const [editingProject, setEditingProject] = useState<Project | null>(null)
    const [loading, setLoading] = useState(true)
    const {user} = useAuth()
    const isAdmin = user?.email === ADMIN_EMAIL

    useEffect(() => {
        fetchProjects()
    }, [maxProjects])

    const fetchProjects = async () => {
        try {
            setLoading(true)
            const q = query(collection(db, 'projects'))
            const querySnapshot = await getDocs(q)
            const projectsData: Project[] = []
            querySnapshot.forEach((doc) => {
                projectsData.push({
                    id: doc.id,
                    ...doc.data(),
                    createdAt: doc.data().createdAt?.toDate().toISOString() || new Date().toISOString(),
                    updatedAt: doc.data().updatedAt?.toDate().toISOString() || new Date().toISOString()
                } as Project)
            })
            setProjects(projectsData.sort((a, b) => {
                const dateA = new Date(a.createdAt).getTime();
                const dateB = new Date(b.createdAt).getTime();
                return new Date(dateB).getTime() - new Date(dateA).getTime();
            }));
        } catch (error) {
            console.error('Error fetching projects:', error)
        } finally {
            setLoading(false)
        }
    }

    const handleCreateProject = async (projectData: Omit<Project, 'id' | 'createdAt' | 'updatedAt'>) => {
        try {
            const newProject = {
                ...projectData,
                createdAt: new Date(),
                updatedAt: new Date()
            }
            await addDoc(collection(db, 'projects'), newProject)
            await fetchProjects()
            setIsFormOpen(false)
        } catch (error) {
            console.error('Error creating project:', error)
            throw error
        }
    }

    const handleUpdateProject = async (projectData: Omit<Project, 'id' | 'createdAt' | 'updatedAt'>) => {
        if (!editingProject) return
        try {
            const projectRef = doc(db, 'projects', editingProject.id)
            await updateDoc(projectRef, {
                ...projectData,
                updatedAt: new Date()
            })
            await fetchProjects()
            setEditingProject(null)
            setIsFormOpen(false)
        } catch (error) {
            console.error('Error updating project:', error)
            throw error
        }
    }

    const handleDeleteProject = async (projectId: string): Promise<void> => {
        if (confirm('Are you sure you want to delete this project?')) {
            try {
                await deleteDoc(doc(db, 'projects', projectId))
                await fetchProjects()
            } catch (error) {
                console.error('Error deleting project:', error)
            }
        }
    }

    const categories: (Project['category'] | 'All')[] = ['All', 'Mobile/Web', 'Metaverse', 'Digital Marketing']

    const filteredProjects = activeCategory === 'All'
        ? projects
        : projects.filter(project => project.category === activeCategory)

    return (
        <section className="py-8" aria-labelledby="portfolio-title">
            <div className="container mx-auto px-4 sm:px-6 lg:px-8">
                {showTitle && (
                    <header className="flex justify-between items-center mb-12">
                        <div>
                            <motion.h2
                                id="portfolio-title"
                                initial={{opacity: 0, y: 20}}
                                whileInView={{opacity: 1, y: 0}}
                                viewport={{once: true}}
                                className="text-3xl font-bold mb-2"
                            >
                                Our Latest Works
                            </motion.h2>
                            <p className="text-gray-600">
                                Discover our newest and featured projects
                                <br/>
                                <span className="text-sm text-gray-400">
        Projects with our privacy agreement are not listed here.
    </span>
                            </p>
                        </div>

                        {isAdmin && (
                            <button
                                onClick={() => {
                                    setEditingProject(null)
                                    setIsFormOpen(true)
                                }}
                                className="bg-black text-white px-6 py-3 rounded text-md hover:bg-gray-900 transition-colors"
                                aria-label="Add new project"
                            >
                                Add Project
                            </button>
                        )}
                    </header>
                )}

                <nav aria-label="Project Categories" className="mb-12">
                    <ul className="flex flex-wrap gap-4">
                        {categories.map((category) => (
                            <li key={category}>
                                <button
                                    onClick={() => setActiveCategory(category)}
                                    className={`
                                        px-4 py-2 rounded text-md transition-colors
                                        ${activeCategory === category
                                        ? 'bg-black text-white'
                                        : 'text-gray-600 hover:text-gray-900'
                                    }
                                    `}
                                    aria-current={activeCategory === category ? 'true' : undefined}
                                >
                                    {category}
                                </button>
                            </li>
                        ))}
                    </ul>
                </nav>

                <div
                    className="grid grid-cols-1 md:grid-cols-2 gap-8"
                    role="feed"
                    aria-busy={loading}
                >
                    {loading ? (
                        [...Array(4)].map((_, i) => (
                            <div key={i} className="animate-pulse">
                                <div className="aspect-[4/3] bg-gray-200 rounded-lg mb-4"/>
                                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"/>
                                <div className="h-4 bg-gray-200 rounded w-1/2"/>
                            </div>
                        ))
                    ) : (
                        filteredProjects.map((project) => (
                            <article
                                key={project.id}
                                className="h-full"
                            >
                                <ProjectCard
                                    project={project}
                                    onProjectClickAction={() => setSelectedProject(project)}
                                    onEditAction={isAdmin ? () => {
                                        setEditingProject(project)
                                        setIsFormOpen(true)
                                    } : undefined}
                                    onDeleteAction={isAdmin ? () => handleDeleteProject(project.id) : undefined}
                                />
                            </article>
                        ))
                    )}
                </div>

                {!loading && filteredProjects.length === 0 && (
                    <div className="text-center py-12">
                        <p className="text-gray-600">There are no projects in this category yet.</p>
                    </div>
                )}
            </div>

            {selectedProject && (
                <ProjectModal
                    project={selectedProject}
                    isOpen={true}
                    onCloseAction={() => setSelectedProject(null)}
                />
            )}

            {isFormOpen && (
                <ProjectForm
                    project={editingProject ? {
                        title: editingProject.title,
                        slug: editingProject.slug,
                        description: editingProject.description,
                        category: editingProject.category,
                        image: editingProject.image,
                        technologies: editingProject.technologies,
                        link: editingProject.link
                    } : undefined}
                    onSubmitAction={editingProject ? handleUpdateProject : handleCreateProject}
                    onCloseAction={() => {
                        setIsFormOpen(false)
                        setEditingProject(null)
                    }}
                />
            )}
        </section>
    )
}