import Link from 'next/link'
import React from "react";

export default function Navbar() {
    return (
        <nav className="fixed w-full z-50 bg-transparent">
            <div className="container mx-auto py-6 flex justify-between items-center">
                <Link href="/" aria-label="Home">
                    <svg width="24" height="24" viewBox="0 0 24 24" className="text-black">
                        <path d="M12 5L5 19h14L12 5z" fill="currentColor"/>
                    </svg>
                </Link>
                <div className="flex items-center space-x-6">
                    <Link href="/blog" className="text-md hover:opacity-70">Blog</Link>
                    <Link href="/contact" className="text-md hover:opacity-70">Contact</Link>
                </div>
            </div>
        </nav>
    )
}
