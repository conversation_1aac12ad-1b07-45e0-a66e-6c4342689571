'use client'
import { useTheme } from 'next-themes'
import { Moon, Sun } from 'lucide-react'
import { useState, useEffect } from 'react'

export function ThemeToggle() {
    const { theme, setTheme } = useTheme()
    const [mounted, setMounted] = useState(false)

    useEffect(() => {
        setMounted(true)
    }, [])

    return (
        <button
            onClick={() => setTheme(theme === 'dark' ? 'light' : 'dark')}
            className="fixed left-4 top-1/4 -translate-y-1/4 p-4 rounded bg-white dark:bg-gray-900 shadow-lg hover:shadow-xl transition-colors md:flex hidden z-50"
            title="Toggle theme"
            aria-label={theme === 'dark' ? 'Switch to light mode' : 'Switch to dark mode'}
        >
            {mounted && (
                <div className="flex items-center justify-center">
                    {theme === 'dark' ? (
                        <Sun size={20} className="text-gray-200 hover:text-gray-100 transition-colors" />
                    ) : (
                        <Moon size={20} className="text-gray-600 hover:text-gray-800 transition-colors" />
                    )}
                </div>
            )}
        </button>
    )
}