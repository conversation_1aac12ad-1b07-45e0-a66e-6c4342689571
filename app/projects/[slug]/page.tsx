import {Metadata} from 'next'
import {collection, query, where, getDocs} from 'firebase/firestore'
import {db} from '@/app/lib/firebase'
import {Project} from '@/app/types/project'
import Image from 'next/image'
import Link from 'next/link'
import {ArrowLeft, Globe, Tag, Layers} from 'lucide-react'
import Header from '@/app/components/Header'
import {ThemeToggle} from '@/app/components/ThemeToggle'
import Footer from '@/app/components/Footer'
import {jsonLdScriptProps} from 'react-schemaorg'
import {CreativeWork, WithContext} from 'schema-dts'

interface PageParams {
    slug: string;
}

interface Props {
    params: Promise<PageParams>;
    searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
}
export async function generateMetadata({ params, searchParams }: Props): Promise<Metadata> {
    const resolvedParams = await params
    await searchParams;
    try {
        const q = query(
            collection(db, 'projects'),
            where('slug', '==', resolvedParams.slug)
        )
        const querySnapshot = await getDocs(q)
        const projectData = querySnapshot.docs[0]?.data()
        const project = {
            ...projectData,
            id: querySnapshot.docs[0].id,
            createdAt: projectData.createdAt?.toDate().toISOString(),
            updatedAt: projectData.updatedAt?.toDate().toISOString()
        } as Project & { id: string }

        if (!project) {
            return {
                title: 'Proje Bulunamadı | feliXart',
                description: 'İstenen proje bulunamadı.',
                robots: {
                    index: false,
                    follow: false
                }
            }
        }

        return {
            title: `${project.title} | feliXart`,
            description: project.description.substring(0, 155) + '...',
            keywords: [...(project.technologies || []), project.category, 'feliXart', 'proje'],
            alternates: {
                canonical: `https://felixart.com/projects/${resolvedParams.slug}`
            },
            openGraph: {
                title: project.title,
                description: project.description.substring(0, 155) + '...',
                type: 'article',
                url: `https://felixart.com/projects/${resolvedParams.slug}`,
                siteName: 'feliXart',
                images: [{
                    url: project.image,
                    width: 1200,
                    height: 630,
                    alt: `${project.title} Proje Görseli`
                }],
                locale: 'tr_TR',
            },
            twitter: {
                card: 'summary_large_image',
                title: project.title,
                description: project.description.substring(0, 155) + '...',
                images: [project.image],
                creator: '@felixart'
            }
        }
    } catch (error) {
        console.error('Error generating metadata:', error)
        return {
            title: 'Proje Detayları | feliXart',
            description: 'Proje detayları ve bilgileri.'
        }
    }
}

export async function generateStaticParams(): Promise<PageParams[]> {
    try {
        const projectsRef = collection(db, 'projects')
        const snapshot = await getDocs(projectsRef)
        return snapshot.docs.map((doc) => ({
            slug: doc.data().slug,
        }))
    } catch (error) {
        console.error('Error generating static params:', error)
        return []
    }
}

export default async function ProjectPage({ params, searchParams }: Props) {
    const resolvedParams = await params
    await searchParams;
    const q = query(collection(db, 'projects'), where('slug', '==', resolvedParams.slug))
    const querySnapshot = await getDocs(q)
    const projectData = querySnapshot.docs[0]?.data()

    if (!projectData) {
        return (
            <>
                <Header/>
                <ThemeToggle/>
                <div className="min-h-screen flex flex-col items-center justify-center p-4">
                    <h1 className="text-2xl font-bold mb-4">Proje Bulunamadı</h1>
                    <p className="text-gray-600 mb-6">Aradığınız proje mevcut değil.</p>
                    <Link
                        href="/projects"
                        className="flex items-center gap-2 text-md bg-black text-white px-6 py-3 rounded hover:bg-gray-900 transition-colors"
                    >
                        <ArrowLeft size={16}/>
                        Projelere Dön
                    </Link>
                </div>
                <Footer/>
            </>
        )
    }

    const project = {
        ...projectData,
        id: querySnapshot.docs[0].id,
        createdAt: projectData.createdAt?.toDate().toISOString(),
        updatedAt: projectData.updatedAt?.toDate().toISOString()
    } as Project & { id: string }

    const schemaData: WithContext<CreativeWork> = {
        "@context": "https://schema.org",
        "@type": "CreativeWork",
        name: project.title,
        description: project.description,
        image: project.image,
        url: `https://felixart.com/projects/${project.slug}`,
        datePublished: new Date(project.createdAt).toISOString(),
        dateModified: new Date(project.updatedAt).toISOString(),
        author: {
            "@type": "Organization",
            name: "feliXart"
        },
        creator: {
            "@type": "Organization",
            name: "feliXart"
        },
        keywords: project.technologies?.join(', '),
        genre: project.category
    }

    return (
        <>
            <Header/>
            <ThemeToggle/>
            <article className="min-h-screen pt-32">
                <script {...jsonLdScriptProps(schemaData)} />
                <div className="container mx-auto px-4 sm:px-6 lg:px-8">
                    <nav className="mb-8" aria-label="Breadcrumb">
                        <ol className="flex items-center space-x-2">
                            <li><Link href="/" className="text-gray-500 hover:text-black">Ana Sayfa</Link></li>
                            <li className="text-gray-300">/</li>
                            <li className="text-black">{project.title}</li>
                        </ol>
                    </nav>

                    <div className="grid lg:grid-cols-[2fr,1fr] gap-12">
                        <div className="space-y-8">
                            <figure className="relative aspect-video rounded-lg overflow-hidden">
                                <Image
                                    src={project.image}
                                    alt={`${project.title} proje görseli - ${project.category} projesi`}
                                    fill
                                    className="object-cover"
                                    sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 800px"
                                    priority
                                    quality={95}
                                />
                            </figure>

                            <header>
                                <h1 className="text-3xl font-bold mb-4">{project.title}</h1>
                                <p className="text-gray-600 leading-relaxed whitespace-pre-wrap">
                                    {project.description}
                                </p>
                            </header>

                            {project.technologies && project.technologies.length > 0 && (
                                <section aria-labelledby="technologies-title">
                                    <h2 id="technologies-title"
                                        className="text-xl font-semibold mb-4 flex items-center gap-2">
                                        <Layers size={20}/>
                                        Kullanılan Teknolojiler
                                    </h2>
                                    <ul className="flex flex-wrap gap-2">
                                        {project.technologies.map((tech) => (
                                            <li
                                                key={tech}
                                                className="px-4 py-2 bg-gray-100 rounded text-md font-medium"
                                            >
                                                {tech}
                                            </li>
                                        ))}
                                    </ul>
                                </section>
                            )}
                        </div>

                        <aside className="lg:sticky lg:top-32 space-y-6 h-fit">
                            <div className="p-6 bg-gray-50 rounded-lg space-y-6">
                                <div className="flex items-start gap-3">
                                    <Tag className="mt-1 text-gray-500" size={20}/>
                                    <div>
                                        <h2 className="font-medium">Kategori</h2>
                                        <p className="text-gray-600">{project.category}</p>
                                    </div>
                                </div>

                                {project.link && (
                                    <div className="flex items-start gap-3">
                                        <Globe className="mt-1 text-gray-500" size={20}/>
                                        <div>
                                            <h2 className="font-medium">Proje URL</h2>
                                            <a
                                                href={project.link}
                                                target="_blank"
                                                rel="noopener noreferrer"
                                                className="text-blue-600 hover:underline"
                                                aria-label={`${project.title} projesini ziyaret et`}
                                            >
                                                Websiteyi Ziyaret Et
                                            </a>
                                        </div>
                                    </div>
                                )}
                            </div>

                            {project.link && (
                                <a
                                    href={project.link}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className="block w-full bg-black text-white text-center px-6 py-3 rounded hover:bg-gray-900 transition-colors"
                                >
                                    Projeyi Ziyaret Et
                                </a>
                            )}
                        </aside>
                    </div>
                </div>
            </article>
            <Footer/>
        </>
    )
}