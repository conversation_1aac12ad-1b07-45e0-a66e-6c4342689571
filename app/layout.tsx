// app/layout.tsx
import type { Metadata } from 'next'
import './globals.css'
import { FirebaseProvider } from '@/app/context/FirebaseContext'
import { ThemeProvider } from '@/app/providers/ThemeProvider'
import { initializeApp } from "firebase/app"
import { firebaseConfig } from '@/app/lib/firebase'
import { jsonLdScriptProps } from 'react-schemaorg'
import { WebSite } from 'schema-dts'

const app = initializeApp(firebaseConfig)

if (typeof window !== 'undefined') {
    import("firebase/analytics").then(({ getAnalytics }) => {
        getAnalytics(app)
    }).catch((error) => {
        console.error("Firebase Analytics yüklenemedi:", error)
    })
}

export const metadata: Metadata = {
    metadataBase: new URL('https://felixart.com.tr'),
    title: {
        default: 'feliXart - New Age Agency',
        template: '%s | feliXart'
    },
    description: 'AR, Mobile Apps, Web, Digital Marketing solutions by feliXart',
    keywords: ['AR', 'Mobile Apps', 'Web Development', 'Digital Marketing', 'Turkish Agency'],
    authors: [{ name: 'feliXart' }],
    creator: 'feliXart',
    publisher: 'feliXart',
    formatDetection: {
        email: false,
        address: false,
        telephone: false,
    },
    openGraph: {
        type: 'website',
        locale: 'tr_TR',
        url: 'https://felixart.com.tr',
        siteName: 'feliXart',
        title: 'feliXart - New Age Agency',
        description: 'AR, Mobile Apps, Web, Digital Marketing solutions',
        images: [
            {
                url: '/og-image.jpg',
                width: 1200,
                height: 630,
                alt: 'feliXart'
            }
        ]
    },
    twitter: {
        card: 'summary_large_image',
        title: 'feliXart - New Age Agency',
        description: 'AR, Mobile Apps, Web, Digital Marketing solutions',
        images: ['/og-image.jpg'],
        creator: '@felixart',
    },
    robots: {
        index: true,
        follow: true,
        googleBot: {
            index: true,
            follow: true,
            'max-video-preview': -1,
            'max-image-preview': 'large',
            'max-snippet': -1,
        },
    },
    verification: {
        google: 'your-google-site-verification',
    },
    alternates: {
        canonical: 'https://felixart.com.tr',
    },
}

export default function RootLayout({
                                       children,
                                   }: {
    children: React.ReactNode
}) {
    return (
        <html lang="tr" suppressHydrationWarning>
        <head>
            <link rel="icon" href="/favicon.svg" type="image/svg+xml"/>
            <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
            <link rel="manifest" href="/manifest.json" />
            <meta name="theme-color" content="#ffffff" media="(prefers-color-scheme: light)" />
            <meta name="theme-color" content="#000000" media="(prefers-color-scheme: dark)" />
            <link rel="canonical" href="https://felixart.com.tr" />
            <script
                {...jsonLdScriptProps<WebSite>({
                    "@context": "https://schema.org",
                    "@type": "WebSite",
                    name: "feliXart",
                    url: "https://felixart.com.tr",
                    potentialAction: {
                        "@type": "SearchAction",
                        target: "https://felixart.com.tr/search?q={search_term_string}",
                        // @ts-expect-error: Gerekli arama terimi
                        "query-input": "required name=search_term_string"
                    },
                    inLanguage: "tr-TR",
                    description: "AR, Mobile Apps, Web, Digital Marketing solutions by feliXart",
                    publisher: {
                        "@type": "Organization",
                        name: "feliXart",
                        logo: {
                            "@type": "ImageObject",
                            url: "https://felixart.com.tr/logo.png"
                        }
                    }
                })}
            />
        </head>
        <body className="antialiased">
        <FirebaseProvider>
            <ThemeProvider
                attribute="class"
                defaultTheme="light"
                enableSystem
                disableTransitionOnChange
            >
                {children}
            </ThemeProvider>
        </FirebaseProvider>
        </body>
        </html>
    )
}