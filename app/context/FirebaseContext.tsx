// context/FirebaseContext.tsx
'use client'
import { createContext, useContext, useState, useEffect, ReactNode } from 'react'
import { User } from 'firebase/auth'
import { auth, storage } from '@/app/lib/firebase'
import { ref, uploadBytes, getDownloadURL } from 'firebase/storage'

interface FirebaseContextType {
    user: User | null;
    loading: boolean;
    uploadFile: (file: File, path: string) => Promise<string>;
}

const FirebaseContext = createContext<FirebaseContextType | undefined>(undefined)

export function FirebaseProvider({ children }: { children: ReactNode }) {
    const [user, setUser] = useState<User | null>(null)
    const [loading, setLoading] = useState(true)

    useEffect(() => {
        const unsubscribe = auth.onAuthStateChanged((user) => {
            setUser(user)
            setLoading(false)
        })

        return unsubscribe
    }, [])

    const uploadFile = async (file: File, path: string): Promise<string> => {
        const storageRef = ref(storage, path)
        await uploadBytes(storageRef, file)
        return await getDownloadURL(storageRef)
    }

    return (
        <FirebaseContext.Provider value={{ user, loading, uploadFile }}>
            {children}
        </FirebaseContext.Provider>
    )
}

export function useFirebase() {
    const context = useContext(FirebaseContext)
    if (context === undefined) {
        throw new Error('useFirebase must be used within a FirebaseProvider')
    }
    return context
}