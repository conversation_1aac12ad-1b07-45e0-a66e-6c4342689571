import { db } from '@/app/lib/firebase'
import { collection, getDocs } from 'firebase/firestore'

export async function GET() {
    try {
        // Firebase'den projeleri çek
        const projectsRef = collection(db, 'projects')
        const snapshot = await getDocs(projectsRef)
        const projects = snapshot.docs.map(doc => doc.data())

        // XML başlığı
        const xml = `<?xml version="1.0" encoding="UTF-8"?>
    <urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
      <!-- Statik sayfalar -->
      <url>
        <loc>https://felixart.com.tr</loc>
        <changefreq>daily</changefreq>
        <priority>1.0</priority>
      </url>
      <url>
        <loc>https://felixart.com.tr/projects</loc>
        <changefreq>weekly</changefreq>
        <priority>0.8</priority>
      </url>
      
      <!-- <PERSON><PERSON>k proje sayfaları -->
      ${projects
            .map(project => `
        <url>
          <loc>https://felixart.com.tr/projects/${project.slug}</loc>
          <lastmod>${project.updatedAt?.toDate().toISOString()}</lastmod>
          <changefreq>weekly</changefreq>
          <priority>0.7</priority>
        </url>
      `).join('')}
    </urlset>`

        // XML response döndür
        return new Response(xml, {
            headers: {
                'Content-Type': 'application/xml',
                'Cache-Control': 'public, max-age=3600, s-maxage=3600'
            },
        })
    } catch (error) {
        console.error('Sitemap generation error:', error)
        return new Response('Error generating sitemap', { status: 500 })
    }
}