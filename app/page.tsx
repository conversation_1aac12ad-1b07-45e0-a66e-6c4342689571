// app/page.tsx
'use client'
import Header from '@/app/components/Header'
import Hero from '@/app/components/Hero'
import About from '@/app/components/About'
import Services from '@/app/components/Services'
import Portfolio from '@/app/components/Portfolio/Portfolio'
import Footer from '@/app/components/Footer'
import { ThemeToggle } from "@/app/components/ThemeToggle"
import Offices from "@/app/components/Offices"
import Join from "@/app/components/Join"



export default function Home() {
    return (
        <>
            <Header />
            <ThemeToggle />
            <main className="pt-8">
                <Hero />
                <About />
                <Services />
                <Portfolio
                    showTitle={true}
                    maxProjects={6}
                />
                <Join />
                <Offices />
            </main>
            <Footer />
        </>
    )
}