/** @type {import('next-sitemap').IConfig} */
module.exports = {
    siteUrl: 'https://felixart.com.tr',
    generateRobotsTxt: true,
    sitemapSize: 7000,
    changefreq: 'weekly',
    priority: 0.7,
    exclude: ['/secret/*'],
    robotsTxtOptions: {
        policies: [
            { userAgent: '*', allow: '/' },
            { userAgent: '*', disallow: '/secret' },
        ],
    },
    additionalPaths: async (config) => {
        const result = await fetch('https://felixart.com.tr/api/sitemap')
        const sitemap = await result.text()
        return [
            {
                loc: '/api/sitemap',
                changefreq: 'daily',
                priority: 0.7,
                lastmod: new Date().toISOString(),
            },
        ]
    },
}